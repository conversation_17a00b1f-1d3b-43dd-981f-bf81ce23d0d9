"""
OpenAI integration and action processing services for the chatbot
"""

import json
import logging
import os
from openai import OpenAI
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from django.conf import settings
from django.contrib.auth.models import User
from django.db import transaction
from django.utils import timezone

from .models import Chat<PERSON>ession, ChatMessage, ChatAction
from .permissions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from leave.models import LeaveRequest, LeaveType, AvailableLeave
from project.models import Project
from employee.models import Employee
from attendance.models import Attendance

# For now, disable project_horilla features to avoid import issues
HORILLA_PROJECT_AVAILABLE = False
Task = None
TimeSheet = None
HorillaProject = None

logger = logging.getLogger(__name__)


class OpenAIService:
    """
    Service class for OpenAI integration using OpenAI library
    """

    def __init__(self):
        # Read API key from environment variable first, then fallback to settings
        self.api_key = os.getenv('OPENAI_API_KEY') or getattr(settings, 'OPENAI_API_KEY', '')
        self.model = os.getenv('OPENAI_MODEL', 'openai/gpt-4o-mini')
        self.max_tokens = int(os.getenv('CHATBOT_MAX_TOKENS', '1000'))
        self.temperature = float(os.getenv('CHATBOT_TEMPERATURE', '0.7'))

        # Initialize OpenAI client
        if self.api_key:
            self.client = OpenAI(api_key=self.api_key, base_url="https://router.requesty.ai/v1",
                                 default_headers={"Authorization": f"Bearer {self.api_key}"})
        else:
            self.client = None

    def generate_response(self, messages: List[Dict[str, str]], user: User) -> Dict[str, Any]:
        """
        Generate response using OpenAI library
        """
        try:
            # Check if OpenAI client is available
            if not self.client:
                logger.error("OpenAI API key not found in environment variables")
                return {
                    "content": "I'm sorry, the AI service is not configured. Please contact your administrator.",
                    "function_call": None
                }

            # Add system context about the user and available actions
            system_context = self._build_system_context(user)

            # Prepare messages for OpenAI
            openai_messages = [{"role": "system", "content": system_context}]
            openai_messages.extend(messages)

            # Get available tools (functions)
            tools = self._get_available_tools(user)

            # Make API call using OpenAI library
            if tools:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=openai_messages,
                    max_tokens=self.max_tokens,
                    temperature=self.temperature,
                    tools=tools,
                    tool_choice="auto"
                )
            else:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=openai_messages,
                    max_tokens=self.max_tokens,
                    temperature=self.temperature
                )

            # Extract message from response
            message = response.choices[0].message

            # Convert to dict format for compatibility
            result = {
                "content": message.content or "",
                "tool_calls": message.tool_calls if hasattr(message, 'tool_calls') else None
            }

            # Convert tool_calls to function_call format for backward compatibility
            if result["tool_calls"]:
                tool_call = result["tool_calls"][0]
                result["function_call"] = {
                    "name": tool_call.function.name,
                    "arguments": tool_call.function.arguments
                }
            else:
                result["function_call"] = None

            return result

        except Exception as e:
            logger.error(f"OpenAI API error: {str(e)}")
            return {
                "content": "I'm sorry, I'm experiencing technical difficulties. Please try again later.",
                "function_call": None
            }

    def _build_system_context(self, user: User) -> str:
        """
        Build system context with user information and available actions
        """
        employee = getattr(user, 'employee_get', None)
        context = f"""
        You are an AI assistant for the HRMS (Human Resource Management System) platform.
        You can help users with various HR-related tasks and provide information.

        Current user: {user.username}
        Employee: {employee.employee_first_name + ' ' + employee.employee_last_name if employee else 'N/A'}

        You can perform the following actions if the user has appropriate permissions:
        - Apply for leave
        - Check leave balance and history
        - Get project summaries and task information
        - Create timesheet entries
        - View employee information
        - Check attendance records
        - Request attendance modifications

        Always check user permissions before performing actions. Be helpful, professional, and concise.
        If a user requests an action they don't have permission for, explain why and suggest alternatives.
        """
        return context

    def _get_available_tools(self, user: User) -> List[Dict]:
        """
        Get list of available tools (functions) based on user permissions
        """
        permission_checker = ChatbotPermissionChecker(user)
        tools = []

        if permission_checker.can_apply_leave():
            tools.append({
                "type": "function",
                "function": {
                    "name": "apply_leave",
                    "description": "Apply for leave",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "leave_type": {"type": "string", "description": "Type of leave"},
                            "start_date": {"type": "string", "description": "Start date (YYYY-MM-DD)"},
                            "end_date": {"type": "string", "description": "End date (YYYY-MM-DD)"},
                            "reason": {"type": "string", "description": "Reason for leave"}
                        },
                        "required": ["leave_type", "start_date", "end_date", "reason"]
                    }
                }
            })

        if permission_checker.can_view_leave_balance():
            tools.append({
                "type": "function",
                "function": {
                    "name": "get_leave_balance",
                    "description": "Get current leave balance",
                    "parameters": {"type": "object", "properties": {}}
                }
            })

        if permission_checker.can_view_project_summary():
            tools.append({
                "type": "function",
                "function": {
                    "name": "get_project_summary",
                    "description": "Get summary of projects",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "project_id": {"type": "integer", "description": "Specific project ID (optional)"}
                        }
                    }
                }
            })

        if permission_checker.can_create_timesheet() and HORILLA_PROJECT_AVAILABLE:
            tools.append({
                "type": "function",
                "function": {
                    "name": "create_timesheet_entry",
                    "description": "Create a timesheet entry",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "project_id": {"type": "integer", "description": "Project ID"},
                            "task_id": {"type": "integer", "description": "Task ID (optional)"},
                            "date": {"type": "string", "description": "Date (YYYY-MM-DD)"},
                            "time_spent": {"type": "string", "description": "Time spent (HH:MM format)"}
                        },
                        "required": ["project_id", "date", "time_spent"]
                    }
                }
            })

        return tools


class ChatbotActionProcessor:
    """
    Service class for processing chatbot actions
    """

    def __init__(self, user: User):
        self.user = user
        self.employee = getattr(user, 'employee_get', None)
        self.permission_checker = ChatbotPermissionChecker(user)

    def process_action(self, action_name: str, parameters: Dict[str, Any], message: ChatMessage) -> Dict[str, Any]:
        """
        Process a chatbot action and return the result
        """
        try:
            # Create action record
            action = ChatAction.objects.create(
                message=message,
                action_type=action_name,
                action_data=parameters,
                status='pending'
            )

            # Process the action
            if action_name == 'apply_leave':
                result = self._apply_leave(parameters, action)
            elif action_name == 'get_leave_balance':
                result = self._get_leave_balance(action)
            elif action_name == 'get_project_summary':
                result = self._get_project_summary(parameters, action)
            elif action_name == 'create_timesheet_entry':
                result = self._create_timesheet_entry(parameters, action)
            else:
                result = {"error": "Unknown action"}
                action.status = 'failed'
                action.error_message = "Unknown action"

            action.result = result
            action.save()

            return result

        except Exception as e:
            logger.error(f"Action processing error: {str(e)}")
            if 'action' in locals():
                action.status = 'failed'
                action.error_message = str(e)
                action.save()
            return {"error": "Action processing failed"}

    def _apply_leave(self, parameters: Dict[str, Any], action: ChatAction) -> Dict[str, Any]:
        """Apply for leave"""
        if not self.permission_checker.can_apply_leave():
            action.status = 'unauthorized'
            return {"error": "You don't have permission to apply for leave"}

        try:
            # Get leave type
            leave_type = LeaveType.objects.filter(name__icontains=parameters['leave_type']).first()
            if not leave_type:
                action.status = 'failed'
                return {"error": f"Leave type '{parameters['leave_type']}' not found"}

            # Create leave request
            leave_request = LeaveRequest.objects.create(
                employee_id=self.employee,
                leave_type_id=leave_type,
                start_date=datetime.strptime(parameters['start_date'], '%Y-%m-%d').date(),
                end_date=datetime.strptime(parameters['end_date'], '%Y-%m-%d').date(),
                description=parameters['reason']
            )

            action.status = 'success'
            return {
                "success": True,
                "message": f"Leave request submitted successfully. Request ID: {leave_request.id}",
                "leave_request_id": leave_request.id
            }

        except Exception as e:
            action.status = 'failed'
            action.error_message = str(e)
            return {"error": f"Failed to apply for leave: {str(e)}"}

    def _get_leave_balance(self, action: ChatAction) -> Dict[str, Any]:
        """Get leave balance"""
        if not self.permission_checker.can_view_leave_balance():
            action.status = 'unauthorized'
            return {"error": "You don't have permission to view leave balance"}

        try:
            available_leaves = AvailableLeave.objects.filter(employee_id=self.employee)

            if not available_leaves.exists():
                action.status = 'success'
                return {
                    "success": True,
                    "message": "No leave balance information found for your account."
                }

            # Format leave balance information for display
            balance_info = []
            for leave in available_leaves:
                total_days = leave.available_days + leave.carryforward_days
                balance_info.append(
                    f"• {leave.leave_type_id.name}: {leave.available_days} days available"
                    + (f" + {leave.carryforward_days} carried forward" if leave.carryforward_days > 0 else "")
                    + f" = {total_days} total days"
                )

            message = "Here's your current leave balance:\n\n" + "\n".join(balance_info)

            action.status = 'success'
            return {
                "success": True,
                "message": message,
                "leave_balance": [
                    {
                        "leave_type": leave.leave_type_id.name,
                        "available_days": leave.available_days,
                        "carryforward_days": leave.carryforward_days,
                        "total_days": leave.available_days + leave.carryforward_days
                    }
                    for leave in available_leaves
                ]
            }

        except Exception as e:
            action.status = 'failed'
            action.error_message = str(e)
            return {"error": f"Failed to get leave balance: {str(e)}"}

    def _get_project_summary(self, parameters: Dict[str, Any], action: ChatAction) -> Dict[str, Any]:
        """Get project summary"""
        try:
            project_id = parameters.get('project_id')

            if project_id:
                if not self.permission_checker.can_view_project_summary(project_id):
                    action.status = 'unauthorized'
                    return {"error": "You don't have permission to view this project"}

            projects = self.permission_checker.get_accessible_projects()

            project_data = []
            for project in projects:
                if project_id and project.id != project_id:
                    continue

                # Handle both basic and horilla project models
                if hasattr(project, 'title'):  # Horilla project
                    project_info = {
                        "id": project.id,
                        "title": project.title,
                        "status": project.status,
                        "start_date": project.start_date.strftime('%Y-%m-%d'),
                        "end_date": project.end_date.strftime('%Y-%m-%d') if project.end_date else None,
                    }
                    if HORILLA_PROJECT_AVAILABLE:
                        tasks = project.task_set.filter(is_active=True)
                        project_info.update({
                            "total_tasks": tasks.count(),
                            "completed_tasks": tasks.filter(status='completed').count()
                        })
                else:  # Basic project
                    project_info = {
                        "id": project.id,
                        "name": project.name,
                        "status": project.status,
                        "start_date": project.start_date.strftime('%Y-%m-%d'),
                        "end_date": project.end_date.strftime('%Y-%m-%d') if project.end_date else None,
                        "project_owner": project.project_owner.employee_first_name if project.project_owner else None
                    }

                project_data.append(project_info)

            # Format project information for display
            if not project_data:
                message = "No projects found that you have access to."
            else:
                if project_id:
                    # Single project details
                    project = project_data[0]
                    if 'title' in project:  # Horilla project
                        message = "Project Details:\n\n"
                        message += f"• Title: {project['title']}\n"
                        message += f"• Status: {project['status']}\n"
                        message += f"• Start Date: {project['start_date']}\n"
                        if project['end_date']:
                            message += f"• End Date: {project['end_date']}\n"
                        if 'total_tasks' in project:
                            message += f"• Total Tasks: {project['total_tasks']}\n"
                            message += f"• Completed Tasks: {project['completed_tasks']}\n"
                    else:  # Basic project
                        message = "Project Details:\n\n"
                        message += f"• Name: {project['name']}\n"
                        message += f"• Status: {project['status']}\n"
                        message += f"• Start Date: {project['start_date']}\n"
                        if project['end_date']:
                            message += f"• End Date: {project['end_date']}\n"
                        if project['project_owner']:
                            message += f"• Owner: {project['project_owner']}\n"
                else:
                    # Multiple projects summary
                    message = f"Here are your accessible projects ({len(project_data)} total):\n\n"
                    for project in project_data[:10]:  # Limit to first 10 projects
                        if 'title' in project:  # Horilla project
                            message += f"• {project['title']} (ID: {project['id']}) - {project['status']}\n"
                        else:  # Basic project
                            message += f"• {project['name']} (ID: {project['id']}) - {project['status']}\n"

                    if len(project_data) > 10:
                        message += f"\n... and {len(project_data) - 10} more projects."

            action.status = 'success'
            return {
                "success": True,
                "message": message,
                "projects": project_data
            }

        except Exception as e:
            action.status = 'failed'
            action.error_message = str(e)
            return {"error": f"Failed to get project summary: {str(e)}"}

    def _create_timesheet_entry(self, parameters: Dict[str, Any], action: ChatAction) -> Dict[str, Any]:
        """Create timesheet entry"""
        if not self.permission_checker.can_create_timesheet() or not HORILLA_PROJECT_AVAILABLE:
            action.status = 'unauthorized'
            return {"error": "You don't have permission to create timesheet entries or timesheet feature is not available"}

        try:
            # Use HorillaProject for timesheet entries
            project = HorillaProject.objects.get(id=parameters['project_id'])
            task = None

            if parameters.get('task_id'):
                task = Task.objects.get(id=parameters['task_id'])

            timesheet = TimeSheet.objects.create(
                employee_id=self.employee,
                project_id=project,
                task_id=task,
                date=datetime.strptime(parameters['date'], '%Y-%m-%d').date(),
                time_spent=parameters['time_spent']
            )

            action.status = 'success'
            return {
                "success": True,
                "message": f"Timesheet entry created successfully. Entry ID: {timesheet.id}",
                "timesheet_id": timesheet.id
            }

        except Exception as e:
            action.status = 'failed'
            action.error_message = str(e)
            return {"error": f"Failed to create timesheet entry: {str(e)}"}
