"""
Management command to test chatbot functionality
"""

from django.core.management.base import <PERSON><PERSON>ommand
from django.contrib.auth.models import User
from chatbot.models import ChatSession, ChatMessage
from chatbot.permissions import ChatbotPermissionChecker


class Command(BaseCommand):
    help = 'Test chatbot functionality'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing chatbot functionality...'))
        
        # Test 1: Check if models are working
        try:
            # Get first user for testing
            user = User.objects.first()
            if not user:
                self.stdout.write(self.style.ERROR('No users found. Please create a user first.'))
                return
            
            self.stdout.write(f'Testing with user: {user.username}')
            
            # Test permission checker
            permission_checker = ChatbotPermissionChecker(user)
            
            self.stdout.write('Testing permissions:')
            self.stdout.write(f'  - Can apply leave: {permission_checker.can_apply_leave()}')
            self.stdout.write(f'  - Can view leave balance: {permission_checker.can_view_leave_balance()}')
            self.stdout.write(f'  - Can view project summary: {permission_checker.can_view_project_summary()}')
            self.stdout.write(f'  - Can create timesheet: {permission_checker.can_create_timesheet()}')
            
            # Test creating a chat session
            session = ChatSession.objects.create(
                user=user,
                employee=getattr(user, 'employee_get', None),
                session_id='test-session-123',
                title='Test Chat Session'
            )
            
            self.stdout.write(f'Created chat session: {session.session_id}')
            
            # Test creating a message
            message = ChatMessage.objects.create(
                session=session,
                message_type='user',
                content='Hello, this is a test message!'
            )
            
            self.stdout.write(f'Created message: {message.content}')
            
            # Clean up
            session.delete()
            
            self.stdout.write(self.style.SUCCESS('All tests passed! Chatbot is ready to use.'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Test failed: {str(e)}'))
            import traceback
            traceback.print_exc()
