"""
Management command to test OpenAI integration with OpenAI library
"""

import os
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from chatbot.services import OpenAIService


class Command(BaseCommand):
    help = 'Test OpenAI integration using OpenAI library'

    def add_arguments(self, parser):
        parser.add_argument(
            '--message',
            type=str,
            default='Hello, can you help me with my leave balance?',
            help='Test message to send to OpenAI'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing OpenAI integration with OpenAI library...'))

        # Check if API key is set
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.stdout.write(
                self.style.WARNING(
                    'OpenAI API key not found in environment variables.\n'
                    'Set OPENAI_API_KEY environment variable to test AI functionality.\n'
                    'Example: export OPENAI_API_KEY=your_api_key_here'
                )
            )
            self.stdout.write('Testing without API key (will show error message)...')
        else:
            self.stdout.write(f'API key found: {api_key[:10]}...')

        try:
            # Get first user for testing
            user = User.objects.first()
            if not user:
                self.stdout.write(self.style.ERROR('No users found. Please create a user first.'))
                return

            self.stdout.write(f'Testing with user: {user.username}')

            # Initialize OpenAI service
            openai_service = OpenAIService()

            # Test message
            test_message = options['message']
            self.stdout.write(f'Sending message: "{test_message}"')

            # Prepare messages
            messages = [{"role": "user", "content": test_message}]

            # Generate response
            response = openai_service.generate_response(messages, user)

            # Display results
            self.stdout.write('\n' + '='*50)
            self.stdout.write('RESPONSE:')
            self.stdout.write('='*50)
            self.stdout.write(f"Content: {response.get('content', 'No content')}")

            if response.get('function_call'):
                self.stdout.write(f"Function Call: {response['function_call']['name']}")
                self.stdout.write(f"Arguments: {response['function_call']['arguments']}")
            else:
                self.stdout.write("No function call detected")

            self.stdout.write('='*50)

            # Show environment variables being used
            self.stdout.write('\nEnvironment Variables:')
            self.stdout.write(f"OPENAI_MODEL: {os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')}")
            self.stdout.write(f"CHATBOT_MAX_TOKENS: {os.getenv('CHATBOT_MAX_TOKENS', '1000')}")
            self.stdout.write(f"CHATBOT_TEMPERATURE: {os.getenv('CHATBOT_TEMPERATURE', '0.7')}")

            if api_key:
                self.stdout.write(self.style.SUCCESS('\n✅ OpenAI integration test completed successfully!'))
            else:
                self.stdout.write(self.style.WARNING('\n⚠️  Test completed but API key is missing for full functionality.'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Test failed: {str(e)}'))
            import traceback
            traceback.print_exc()
