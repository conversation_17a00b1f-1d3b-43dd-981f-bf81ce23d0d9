"""
Admin configuration for chatbot models
"""

from django.contrib import admin
from .models import ChatSession, ChatMessage, ChatAction, ChatbotSettings


@admin.register(ChatSession)
class ChatSessionAdmin(admin.ModelAdmin):
    list_display = ['session_id', 'user', 'employee', 'title', 'created_at', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['session_id', 'user__username', 'employee__employee_first_name', 'title']
    readonly_fields = ['session_id', 'created_at', 'updated_at']


@admin.register(ChatMessage)
class ChatMessageAdmin(admin.ModelAdmin):
    list_display = ['session', 'message_type', 'content_preview', 'timestamp']
    list_filter = ['message_type', 'timestamp']
    search_fields = ['content', 'session__session_id']
    readonly_fields = ['timestamp']

    def content_preview(self, obj):
        return obj.content[:100] + "..." if len(obj.content) > 100 else obj.content
    content_preview.short_description = 'Content Preview'


@admin.register(ChatAction)
class ChatActionAdmin(admin.ModelAdmin):
    list_display = ['action_type', 'status', 'message', 'executed_at']
    list_filter = ['action_type', 'status', 'executed_at']
    search_fields = ['action_type', 'message__content']
    readonly_fields = ['executed_at']


@admin.register(ChatbotSettings)
class ChatbotSettingsAdmin(admin.ModelAdmin):
    list_display = ['user', 'is_enabled', 'preferred_language', 'created_at']
    list_filter = ['is_enabled', 'preferred_language']
    search_fields = ['user__username']
    readonly_fields = ['created_at', 'updated_at']
