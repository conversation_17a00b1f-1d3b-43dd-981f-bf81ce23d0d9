"""
Chatbot models for HRMS platform
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from employee.models import Employee


class ChatSession(models.Model):
    """
    Model to store chat sessions for users
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chat_sessions')
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='chat_sessions')
    session_id = models.CharField(max_length=100, unique=True)
    title = models.Char<PERSON>ield(max_length=200, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-updated_at']

    def __str__(self):
        return f"Chat Session {self.session_id} - {self.user.username}"


class ChatMessage(models.Model):
    """
    Model to store individual chat messages
    """
    MESSAGE_TYPES = [
        ('user', 'User Message'),
        ('assistant', 'Assistant Message'),
        ('system', 'System Message'),
        ('action', 'Action Result'),
    ]

    session = models.ForeignKey(ChatSession, on_delete=models.CASCADE, related_name='messages')
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES)
    content = models.TextField()
    metadata = models.JSONField(default=dict, blank=True)  # Store additional data like action results
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['timestamp']

    def __str__(self):
        return f"{self.message_type}: {self.content[:50]}..."


class ChatAction(models.Model):
    """
    Model to track actions performed by the chatbot
    """
    ACTION_TYPES = [
        ('leave_apply', 'Apply for Leave'),
        ('leave_balance', 'Check Leave Balance'),
        ('leave_history', 'View Leave History'),
        ('project_summary', 'Get Project Summary'),
        ('task_update', 'Update Task Status'),
        ('timesheet_entry', 'Create Timesheet Entry'),
        ('employee_info', 'Get Employee Information'),
        ('attendance_check', 'Check Attendance'),
        ('attendance_request', 'Request Attendance Modification'),
    ]

    ACTION_STATUS = [
        ('pending', 'Pending'),
        ('success', 'Success'),
        ('failed', 'Failed'),
        ('unauthorized', 'Unauthorized'),
    ]

    message = models.ForeignKey(ChatMessage, on_delete=models.CASCADE, related_name='actions')
    action_type = models.CharField(max_length=50, choices=ACTION_TYPES)
    action_data = models.JSONField(default=dict)  # Store action parameters
    status = models.CharField(max_length=20, choices=ACTION_STATUS, default='pending')
    result = models.JSONField(default=dict, blank=True)  # Store action results
    error_message = models.TextField(blank=True, null=True)
    executed_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.action_type} - {self.status}"


class ChatbotSettings(models.Model):
    """
    Model to store chatbot configuration settings
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='chatbot_settings')
    is_enabled = models.BooleanField(default=True)
    preferred_language = models.CharField(max_length=10, default='en')
    notification_preferences = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Chatbot Settings - {self.user.username}"
