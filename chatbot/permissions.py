"""
Permission utilities for chatbot actions
"""

from django.contrib.auth.models import User
from django.db import models
from employee.models import Employee
from leave.models import LeaveRequest, AvailableLeave
from project.models import Project
from attendance.models import Attendance

# For now, disable project_horilla features to avoid import issues
HORILLA_PROJECT_AVAILABLE = False
Task = None
TimeSheet = None
HorillaProject = None


class ChatbotPermissionChecker:
    """
    Utility class to check user permissions for various chatbot actions
    """

    def __init__(self, user: User):
        self.user = user
        self.employee = getattr(user, 'employee_get', None)

    def can_apply_leave(self) -> bool:
        """Check if user can apply for leave"""
        return self.user.has_perm('leave.add_leaverequest') or self.employee is not None

    def can_view_leave_balance(self) -> bool:
        """Check if user can view their leave balance"""
        return self.employee is not None

    def can_view_leave_history(self) -> bool:
        """Check if user can view their leave history"""
        return self.employee is not None

    def can_view_project_summary(self, project_id: int = None) -> bool:
        """Check if user can view project information"""
        if self.user.has_perm('project.view_project'):
            return True

        if not self.employee:
            return False

        # Check basic project model first
        if project_id:
            try:
                project = Project.objects.get(id=project_id)
                # Basic project model doesn't have managers/members, so check if user is project owner
                return project.project_owner == self.employee or self.employee in project.team_members.all()
            except Project.DoesNotExist:
                pass

        # Check horilla project model if available
        if HORILLA_PROJECT_AVAILABLE and project_id:
            try:
                project = HorillaProject.objects.get(id=project_id)
                return (self.employee in project.managers.all() or
                       self.employee in project.members.all())
            except HorillaProject.DoesNotExist:
                return False

        return True  # Allow viewing project summaries if no specific project ID

    def can_update_task(self, task_id: int = None) -> bool:
        """Check if user can update task status"""
        if self.user.has_perm('project.change_task'):
            return True

        if not self.employee or not task_id or not HORILLA_PROJECT_AVAILABLE:
            return False

        try:
            task = Task.objects.get(id=task_id)
            return (self.employee in task.task_managers.all() or
                   self.employee in task.task_members.all() or
                   self.employee in task.project.managers.all() or
                   self.employee in task.project.members.all())
        except Task.DoesNotExist:
            return False

    def can_create_timesheet(self) -> bool:
        """Check if user can create timesheet entries"""
        return self.employee is not None and HORILLA_PROJECT_AVAILABLE

    def can_view_employee_info(self, employee_id: int = None) -> bool:
        """Check if user can view employee information"""
        if self.user.has_perm('employee.view_employee'):
            return True

        # Users can always view their own information
        if employee_id is None or (self.employee and self.employee.id == employee_id):
            return True

        # Check if user is a manager of the requested employee
        if self.employee:
            try:
                target_employee = Employee.objects.get(id=employee_id)
                return (hasattr(target_employee, 'employee_work_info') and
                       target_employee.employee_work_info.reporting_manager_id == self.employee)
            except Employee.DoesNotExist:
                return False

        return False

    def can_view_attendance(self, employee_id: int = None) -> bool:
        """Check if user can view attendance records"""
        if self.user.has_perm('attendance.view_attendance'):
            return True

        # Users can view their own attendance
        if employee_id is None or (self.employee and self.employee.id == employee_id):
            return True

        return False

    def can_request_attendance_modification(self) -> bool:
        """Check if user can request attendance modifications"""
        return self.employee is not None

    def get_accessible_projects(self):
        """Get list of projects user has access to"""
        if self.user.has_perm('project.view_project'):
            # Return basic projects if horilla projects not available
            if not HORILLA_PROJECT_AVAILABLE:
                return Project.objects.all()
            else:
                return HorillaProject.objects.filter(is_active=True)

        if not self.employee:
            if not HORILLA_PROJECT_AVAILABLE:
                return Project.objects.none()
            else:
                return HorillaProject.objects.none()

        # Return basic projects if horilla projects not available
        if not HORILLA_PROJECT_AVAILABLE:
            return Project.objects.filter(
                models.Q(project_owner=self.employee) | models.Q(team_members=self.employee)
            ).distinct()
        else:
            return HorillaProject.objects.filter(
                models.Q(managers=self.employee) | models.Q(members=self.employee),
                is_active=True
            ).distinct()

    def get_accessible_tasks(self):
        """Get list of tasks user has access to"""
        if not HORILLA_PROJECT_AVAILABLE:
            return []  # No tasks available without horilla project

        if self.user.has_perm('project.view_task'):
            return Task.objects.filter(is_active=True)

        if not self.employee:
            return Task.objects.none()

        return Task.objects.filter(
            models.Q(task_managers=self.employee) |
            models.Q(task_members=self.employee) |
            models.Q(project__managers=self.employee) |
            models.Q(project__members=self.employee),
            is_active=True
        ).distinct()
