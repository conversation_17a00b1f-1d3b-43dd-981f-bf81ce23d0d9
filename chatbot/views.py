"""
Views for the chatbot application
"""

import json
import uuid
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from django.contrib.auth.models import User
from django.db import transaction
from django.conf import settings

from .models import ChatSession, ChatMessage, ChatAction
from .services import OpenAIService, ChatbotActionProcessor


@login_required
def chat_interface(request):
    """
    Main chat interface view
    """
    # Get or create active chat session
    session = ChatSession.objects.filter(
        user=request.user,
        is_active=True
    ).first()

    if not session:
        session = ChatSession.objects.create(
            user=request.user,
            employee=request.user.employee_get,
            session_id=str(uuid.uuid4()),
            title="New Chat Session"
        )

    # Get recent messages
    messages = session.messages.all()[:50]  # Limit to last 50 messages

    context = {
        'session': session,
        'messages': messages,
        'user': request.user,
        'employee': request.user.employee_get
    }

    return render(request, 'chatbot/chat_interface.html', context)


@method_decorator(login_required, name='dispatch')
@method_decorator(csrf_exempt, name='dispatch')
class ChatAPIView(View):
    """
    API view for handling chat interactions
    """

    def post(self, request):
        """
        Handle chat message and generate response
        """
        try:
            data = json.loads(request.body)
            user_message = data.get('message', '').strip()
            session_id = data.get('session_id')

            if not user_message:
                return JsonResponse({'error': 'Message is required'}, status=400)

            # Get or create chat session
            if session_id:
                session = get_object_or_404(ChatSession, session_id=session_id, user=request.user)
            else:
                session = ChatSession.objects.create(
                    user=request.user,
                    employee=request.user.employee_get,
                    session_id=str(uuid.uuid4()),
                    title=user_message[:50] + "..." if len(user_message) > 50 else user_message
                )

            # Save user message
            user_msg = ChatMessage.objects.create(
                session=session,
                message_type='user',
                content=user_message
            )

            # Prepare conversation history for OpenAI
            recent_messages = session.messages.order_by('-timestamp')[:10]
            conversation_history = []

            for msg in reversed(recent_messages):
                if msg.message_type == 'user':
                    conversation_history.append({"role": "user", "content": msg.content})
                elif msg.message_type == 'assistant':
                    conversation_history.append({"role": "assistant", "content": msg.content})

            # Generate response using OpenAI
            openai_service = OpenAIService()
            ai_response = openai_service.generate_response(conversation_history, request.user)

            # Process function calls if any
            action_results = []
            if ai_response.get('function_call'):
                action_processor = ChatbotActionProcessor(request.user)
                function_name = ai_response['function_call']['name']
                function_args = json.loads(ai_response['function_call']['arguments'])

                # Create assistant message for the function call
                assistant_msg = ChatMessage.objects.create(
                    session=session,
                    message_type='assistant',
                    content=f"I'll help you with {function_name}. Processing your request...",
                    metadata={'function_call': ai_response['function_call']}
                )

                # Process the action
                result = action_processor.process_action(function_name, function_args, assistant_msg)
                action_results.append(result)
                print(f"Action result: {result}")  # Debug print

                # Create action result message
                if result.get('success'):
                    action_msg_content = result.get('message', 'Action completed successfully.')
                else:
                    action_msg_content = result.get('error', 'Action failed.')

                action_msg = ChatMessage.objects.create(
                    session=session,
                    message_type='action',
                    content=action_msg_content,
                    metadata={'action_result': result}
                )

                response_content = action_msg_content
            else:
                # Regular text response
                response_content = ai_response.get('content', 'I apologize, but I could not generate a response.')

                # Save assistant response
                assistant_msg = ChatMessage.objects.create(
                    session=session,
                    message_type='assistant',
                    content=response_content
                )

            # Update session
            session.save()  # This will update the updated_at timestamp

            return JsonResponse({
                'success': True,
                'response': response_content,
                'session_id': session.session_id,
                'action_results': action_results,
                'message_id': assistant_msg.id if 'assistant_msg' in locals() else None
            })

        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Chatbot API error: {str(e)}", exc_info=True)
            print(f"Chatbot API error: {str(e)}")
            return JsonResponse({
                'error': 'Sorry, I encountered an error. Please try again.',
                'debug_info': str(e) if hasattr(settings, 'DEBUG') and settings.DEBUG else None
            }, status=500)


@login_required
@require_http_methods(["GET"])
def get_chat_history(request, session_id):
    """
    Get chat history for a specific session
    """
    try:
        session = get_object_or_404(ChatSession, session_id=session_id, user=request.user)
        messages = session.messages.all()

        message_data = []
        for msg in messages:
            message_data.append({
                'id': msg.id,
                'type': msg.message_type,
                'content': msg.content,
                'timestamp': msg.timestamp.isoformat(),
                'metadata': msg.metadata
            })

        return JsonResponse({
            'success': True,
            'session_id': session.session_id,
            'title': session.title,
            'messages': message_data
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to get chat history: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_chat_sessions(request):
    """
    Get list of user's chat sessions
    """
    try:
        sessions = ChatSession.objects.filter(user=request.user).order_by('-updated_at')[:20]

        session_data = []
        for session in sessions:
            last_message = session.messages.last()
            session_data.append({
                'session_id': session.session_id,
                'title': session.title,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat(),
                'is_active': session.is_active,
                'last_message': last_message.content[:100] if last_message else None,
                'message_count': session.messages.count()
            })

        return JsonResponse({
            'success': True,
            'sessions': session_data
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to get chat sessions: {str(e)}'
        }, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def create_new_session(request):
    """
    Create a new chat session
    """
    try:
        # Deactivate current active sessions
        ChatSession.objects.filter(user=request.user, is_active=True).update(is_active=False)

        # Create new session
        session = ChatSession.objects.create(
            user=request.user,
            employee=request.user.employee_get,
            session_id=str(uuid.uuid4()),
            title="New Chat Session",
            is_active=True
        )

        return JsonResponse({
            'success': True,
            'session_id': session.session_id,
            'message': 'New chat session created successfully'
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Failed to create new session: {str(e)}'
        }, status=500)


@login_required
def chat_widget(request):
    """
    Embeddable chat widget view
    """
    return render(request, 'chatbot/chat_widget.html', {
        'user': request.user,
        'employee': request.user.employee_get
    })


def simple_chat(request):
    """
    Simple chat interface for testing (no login required)
    """
    # Create a test session for demo purposes
    session_id = "demo-session-123"

    context = {
        'session': {'session_id': session_id},
        'messages': [],
        'user': {'username': 'demo_user', 'first_name': 'Demo'}
    }

    return render(request, 'chatbot/simple_chat.html', context)
