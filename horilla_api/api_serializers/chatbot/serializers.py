"""
Serializers for chatbot API
"""

from rest_framework import serializers
from chatbot.models import ChatSession, ChatMessage, ChatAction, ChatbotSettings


class ChatSessionSerializer(serializers.ModelSerializer):
    """
    Serializer for ChatSession model
    """
    message_count = serializers.SerializerMethodField()
    last_message = serializers.SerializerMethodField()
    
    class Meta:
        model = ChatSession
        fields = [
            'session_id', 'title', 'created_at', 'updated_at', 
            'is_active', 'message_count', 'last_message'
        ]
        read_only_fields = ['session_id', 'created_at', 'updated_at']

    def get_message_count(self, obj):
        return obj.messages.count()

    def get_last_message(self, obj):
        last_message = obj.messages.last()
        if last_message:
            return {
                'content': last_message.content[:100],
                'timestamp': last_message.timestamp,
                'type': last_message.message_type
            }
        return None


class ChatMessageSerializer(serializers.ModelSerializer):
    """
    Serializer for ChatMessage model
    """
    
    class Meta:
        model = ChatMessage
        fields = [
            'id', 'message_type', 'content', 'metadata', 'timestamp'
        ]
        read_only_fields = ['id', 'timestamp']


class ChatActionSerializer(serializers.ModelSerializer):
    """
    Serializer for ChatAction model
    """
    
    class Meta:
        model = ChatAction
        fields = [
            'id', 'action_type', 'action_data', 'status', 
            'result', 'error_message', 'executed_at'
        ]
        read_only_fields = ['id', 'executed_at']


class ChatbotSettingsSerializer(serializers.ModelSerializer):
    """
    Serializer for ChatbotSettings model
    """
    
    class Meta:
        model = ChatbotSettings
        fields = [
            'is_enabled', 'preferred_language', 'notification_preferences',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class ChatRequestSerializer(serializers.Serializer):
    """
    Serializer for chat request data
    """
    message = serializers.CharField(max_length=2000, required=True)
    session_id = serializers.CharField(max_length=100, required=False, allow_null=True)

    def validate_message(self, value):
        if not value.strip():
            raise serializers.ValidationError("Message cannot be empty")
        return value.strip()


class ChatResponseSerializer(serializers.Serializer):
    """
    Serializer for chat response data
    """
    response = serializers.CharField()
    session_id = serializers.CharField()
    action_results = serializers.ListField(required=False)
    message_id = serializers.IntegerField(required=False, allow_null=True)


class ActionRequestSerializer(serializers.Serializer):
    """
    Serializer for action request data
    """
    action_type = serializers.CharField(max_length=50)
    parameters = serializers.JSONField()
    session_id = serializers.CharField(max_length=100)


class LeaveApplicationSerializer(serializers.Serializer):
    """
    Serializer for leave application through chatbot
    """
    leave_type = serializers.CharField(max_length=100)
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    reason = serializers.CharField(max_length=500)

    def validate(self, data):
        if data['start_date'] > data['end_date']:
            raise serializers.ValidationError("Start date cannot be after end date")
        return data


class TimesheetEntrySerializer(serializers.Serializer):
    """
    Serializer for timesheet entry through chatbot
    """
    project_id = serializers.IntegerField()
    task_id = serializers.IntegerField(required=False, allow_null=True)
    date = serializers.DateField()
    time_spent = serializers.CharField(max_length=10)

    def validate_time_spent(self, value):
        # Validate time format (HH:MM)
        import re
        if not re.match(r'^\d{1,2}:\d{2}$', value):
            raise serializers.ValidationError("Time must be in HH:MM format")
        return value


class ProjectSummaryRequestSerializer(serializers.Serializer):
    """
    Serializer for project summary request
    """
    project_id = serializers.IntegerField(required=False, allow_null=True)
    include_tasks = serializers.BooleanField(default=True)
    include_timesheet = serializers.BooleanField(default=False)
