"""
API views for chatbot
"""

import json
import uuid
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from rest_framework import status
from django.shortcuts import get_object_or_404

from chatbot.models import Chat<PERSON>ess<PERSON>, ChatMessage, ChatAction
from chatbot.services import OpenAIService, ChatbotActionProcessor
from horilla_api.api_serializers.chatbot.serializers import (
    ChatSessionSerializer,
    ChatMessageSerializer,
    ChatActionSerializer,
    ChatRequestSerializer,
    ChatResponseSerializer
)


class ChatAPIView(APIView):
    """
    API view for chat interactions
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Handle chat message and generate response
        """
        serializer = ChatRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        user_message = serializer.validated_data['message']
        session_id = serializer.validated_data.get('session_id')

        try:
            # Get or create chat session
            if session_id:
                session = get_object_or_404(ChatSession, session_id=session_id, user=request.user)
            else:
                session = ChatSession.objects.create(
                    user=request.user,
                    employee=request.user.employee_get,
                    session_id=str(uuid.uuid4()),
                    title=user_message[:50] + "..." if len(user_message) > 50 else user_message
                )

            # Save user message
            user_msg = ChatMessage.objects.create(
                session=session,
                message_type='user',
                content=user_message
            )

            # Prepare conversation history
            recent_messages = session.messages.order_by('-timestamp')[:10]
            conversation_history = []

            for msg in reversed(recent_messages):
                if msg.message_type == 'user':
                    conversation_history.append({"role": "user", "content": msg.content})
                elif msg.message_type == 'assistant':
                    conversation_history.append({"role": "assistant", "content": msg.content})

            # Generate response using OpenAI
            openai_service = OpenAIService()
            ai_response = openai_service.generate_response(conversation_history, request.user)

            # Process function calls if any
            action_results = []
            if ai_response.get('function_call'):
                action_processor = ChatbotActionProcessor(request.user)
                function_name = ai_response['function_call']['name']
                function_args = json.loads(ai_response['function_call']['arguments'])

                # Create assistant message for the function call
                assistant_msg = ChatMessage.objects.create(
                    session=session,
                    message_type='assistant',
                    content=f"I'll help you with {function_name}. Processing your request...",
                    metadata={'function_call': ai_response['function_call']}
                )

                # Process the action
                result = action_processor.process_action(function_name, function_args, assistant_msg)
                action_results.append(result)

                # Create action result message
                if result.get('success'):
                    action_msg_content = result.get('message', 'Action completed successfully.')
                else:
                    action_msg_content = result.get('error', 'Action failed.')

                action_msg = ChatMessage.objects.create(
                    session=session,
                    message_type='action',
                    content=action_msg_content,
                    metadata={'action_result': result}
                )

                response_content = action_msg_content
            else:
                # Regular text response
                response_content = ai_response.get('content', 'I apologize, but I could not generate a response.')

                # Save assistant response
                assistant_msg = ChatMessage.objects.create(
                    session=session,
                    message_type='assistant',
                    content=response_content
                )

            # Update session
            session.save()

            response_data = {
                'response': response_content,
                'session_id': session.session_id,
                'action_results': action_results,
                'message_id': assistant_msg.id if 'assistant_msg' in locals() else None
            }

            return Response(ChatResponseSerializer(response_data).data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': f'An error occurred: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ChatSessionListAPIView(APIView):
    """
    API view for listing chat sessions
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get list of user's chat sessions
        """
        sessions = ChatSession.objects.filter(user=request.user).order_by('-updated_at')
        
        paginator = PageNumberPagination()
        page = paginator.paginate_queryset(sessions, request)
        
        serializer = ChatSessionSerializer(page, many=True)
        return paginator.get_paginated_response(serializer.data)

    def post(self, request):
        """
        Create a new chat session
        """
        try:
            # Deactivate current active sessions
            ChatSession.objects.filter(user=request.user, is_active=True).update(is_active=False)

            # Create new session
            session = ChatSession.objects.create(
                user=request.user,
                employee=request.user.employee_get,
                session_id=str(uuid.uuid4()),
                title="New Chat Session",
                is_active=True
            )

            serializer = ChatSessionSerializer(session)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'error': f'Failed to create new session: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ChatSessionDetailAPIView(APIView):
    """
    API view for chat session details
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, session_id):
        """
        Get chat session details
        """
        session = get_object_or_404(ChatSession, session_id=session_id, user=request.user)
        serializer = ChatSessionSerializer(session)
        return Response(serializer.data)

    def patch(self, request, session_id):
        """
        Update chat session
        """
        session = get_object_or_404(ChatSession, session_id=session_id, user=request.user)
        serializer = ChatSessionSerializer(session, data=request.data, partial=True)
        
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, session_id):
        """
        Delete chat session
        """
        session = get_object_or_404(ChatSession, session_id=session_id, user=request.user)
        session.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ChatMessageListAPIView(APIView):
    """
    API view for chat messages
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, session_id):
        """
        Get messages for a chat session
        """
        session = get_object_or_404(ChatSession, session_id=session_id, user=request.user)
        messages = session.messages.all()
        
        paginator = PageNumberPagination()
        page = paginator.paginate_queryset(messages, request)
        
        serializer = ChatMessageSerializer(page, many=True)
        return paginator.get_paginated_response(serializer.data)


class ChatActionListAPIView(APIView):
    """
    API view for chat actions
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get list of user's chat actions
        """
        actions = ChatAction.objects.filter(
            message__session__user=request.user
        ).order_by('-executed_at')
        
        paginator = PageNumberPagination()
        page = paginator.paginate_queryset(actions, request)
        
        serializer = ChatActionSerializer(page, many=True)
        return paginator.get_paginated_response(serializer.data)
