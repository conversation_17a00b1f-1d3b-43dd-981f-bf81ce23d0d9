{% extends 'index.html' %}
{% load static %}
{% load i18n %}

{% block content %}
<link rel="stylesheet" href="{% static 'chatbot/css/chat.css' %}">
<style>
    .chat-container {
        height: calc(100vh - 200px);
        display: flex;
        flex-direction: column;
        background: #f8f9fa;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .chat-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        text-align: center;
    }

    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background: white;
    }

    .message {
        margin-bottom: 15px;
        display: flex;
        align-items: flex-start;
    }

    .message.user {
        justify-content: flex-end;
    }

    .message.assistant {
        justify-content: flex-start;
    }

    .message-content {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 18px;
        word-wrap: break-word;
    }

    .message.user .message-content {
        background: #007bff;
        color: white;
        border-bottom-right-radius: 4px;
    }

    .message.assistant .message-content {
        background: #e9ecef;
        color: #333;
        border-bottom-left-radius: 4px;
    }

    .message.action .message-content {
        background: #28a745;
        color: white;
        border-radius: 8px;
    }

    .message-time {
        font-size: 0.75rem;
        color: #6c757d;
        margin: 5px 10px 0;
    }

    .chat-input-container {
        padding: 20px;
        background: #f8f9fa;
        border-top: 1px solid #dee2e6;
    }

    .chat-input-form {
        display: flex;
        gap: 10px;
    }

    .chat-input {
        flex: 1;
        padding: 12px 16px;
        border: 1px solid #ced4da;
        border-radius: 25px;
        outline: none;
        font-size: 14px;
    }

    .chat-input:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .send-button {
        padding: 12px 20px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;
    }

    .send-button:hover {
        background: #0056b3;
    }

    .send-button:disabled {
        background: #6c757d;
        cursor: not-allowed;
    }

    .typing-indicator {
        display: none;
        padding: 10px;
        font-style: italic;
        color: #6c757d;
    }

    .session-info {
        background: white;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }

    .new-session-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin-left: 10px;
    }

    .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
        border: 1px solid #f5c6cb;
    }
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="session-info">
                <h5>{% trans "HRMS Chatbot" %}</h5>
                <p>{% trans "Hello" %} {{ user.first_name|default:user.username }}! {% trans "I'm your HRMS assistant. I can help you with leave applications, project information, timesheet entries, and more." %}</p>
                <small>{% trans "Session ID:" %} {{ session.session_id }}</small>
                <button class="new-session-btn" onclick="startNewSession()">{% trans "New Session" %}</button>
            </div>

            <div class="chat-container">
                <div class="chat-header">
                    <h4>{% trans "HRMS Assistant" %}</h4>
                    <small>{% trans "Powered by AI" %}</small>
                </div>

                <div class="chat-messages" id="chatMessages">
                    {% for message in messages %}
                    <div class="message {{ message.message_type }}">
                        <div class="message-content">
                            {{ message.content }}
                        </div>
                        <div class="message-time">
                            {{ message.timestamp|date:"H:i" }}
                        </div>
                    </div>
                    {% empty %}
                    <div class="message assistant">
                        <div class="message-content">
                            {% trans "Hello! I'm your HRMS assistant. How can I help you today?" %}
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <div class="typing-indicator" id="typingIndicator">
                    {% trans "Assistant is typing..." %}
                </div>

                <div class="chat-input-container">
                    <form class="chat-input-form" id="chatForm">
                        {% csrf_token %}
                        <input
                            type="text"
                            class="chat-input"
                            id="messageInput"
                            placeholder="{% trans 'Type your message here...' %}"
                            autocomplete="off"
                            required
                        >
                        <button type="submit" class="send-button" id="sendButton">
                            {% trans "Send" %}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="{% static 'chatbot/js/chat.js' %}"></script>
<script>
    // Initialize chat with session data
    const chatApp = new ChatApp({
        sessionId: '{{ session.session_id }}',
        csrfToken: '{{ csrf_token }}',
        apiUrl: '{% url "chatbot:chat_api" %}',
        newSessionUrl: '{% url "chatbot:new_session" %}'
    });

    // Make chatApp globally available
    window.chatApp = chatApp;
</script>
{% endblock %}
