{% load static %}
{% load i18n %}

<!-- Chat Widget Toggle Button -->
<button class="chat-widget-toggle" id="chatWidgetToggle" onclick="toggleChatWidget()">
    💬
</button>

<!-- Chat Widget -->
<div class="chat-widget" id="chatWidget" style="display: none;">
    <div class="chat-header">
        <h6 style="margin: 0;">{% trans "HRMS Assistant" %}</h6>
        <button onclick="toggleChatWidget()" style="position: absolute; top: 10px; right: 15px; background: none; border: none; color: white; font-size: 18px; cursor: pointer;">×</button>
    </div>
    
    <div class="chat-messages" id="widgetChatMessages">
        <div class="message assistant">
            <div class="message-content">
                {% trans "Hello! I'm your HRMS assistant. How can I help you today?" %}
            </div>
        </div>
    </div>
    
    <div class="typing-indicator" id="widgetTypingIndicator">
        {% trans "Assistant is typing..." %}
    </div>
    
    <div class="chat-input-container">
        <form class="chat-input-form" id="widgetChatForm">
            <input 
                type="text" 
                class="chat-input" 
                id="widgetMessageInput" 
                placeholder="{% trans 'Type your message...' %}"
                autocomplete="off"
                required
            >
            <button type="submit" class="send-button" id="widgetSendButton">
                {% trans "Send" %}
            </button>
        </form>
    </div>
</div>

<link rel="stylesheet" href="{% static 'chatbot/css/chat.css' %}">
<script src="{% static 'chatbot/js/chat.js' %}"></script>

<script>
let widgetChatApp = null;
let widgetSessionId = null;

function toggleChatWidget() {
    const widget = document.getElementById('chatWidget');
    const toggle = document.getElementById('chatWidgetToggle');
    
    if (widget.style.display === 'none') {
        widget.style.display = 'flex';
        toggle.style.display = 'none';
        
        // Initialize chat app if not already done
        if (!widgetChatApp) {
            initializeWidgetChat();
        }
        
        // Focus input
        document.getElementById('widgetMessageInput').focus();
    } else {
        widget.style.display = 'none';
        toggle.style.display = 'block';
    }
}

function initializeWidgetChat() {
    // Create a custom chat app for the widget
    class WidgetChatApp {
        constructor() {
            this.sessionId = null;
            this.csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
            this.apiUrl = '/chatbot/api/chat/';
            
            this.chatMessages = document.getElementById('widgetChatMessages');
            this.messageInput = document.getElementById('widgetMessageInput');
            this.sendButton = document.getElementById('widgetSendButton');
            this.chatForm = document.getElementById('widgetChatForm');
            this.typingIndicator = document.getElementById('widgetTypingIndicator');
            
            this.init();
        }
        
        init() {
            this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
            this.messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.handleSubmit(e);
                }
            });
        }
        
        async handleSubmit(e) {
            e.preventDefault();
            
            const message = this.messageInput.value.trim();
            if (!message) return;
            
            this.setInputState(false);
            this.addMessage('user', message);
            this.messageInput.value = '';
            this.showTypingIndicator();
            
            try {
                const response = await this.sendMessage(message);
                
                if (response.error) {
                    this.addMessage('assistant', `Error: ${response.error}`, 'error');
                } else {
                    this.addMessage('assistant', response.response);
                    
                    if (response.session_id) {
                        this.sessionId = response.session_id;
                    }
                    
                    if (response.action_results && response.action_results.length > 0) {
                        response.action_results.forEach(result => {
                            if (result.success) {
                                this.addMessage('action', result.message || 'Action completed successfully');
                            } else {
                                this.addMessage('action', result.error || 'Action failed', 'error');
                            }
                        });
                    }
                }
            } catch (error) {
                console.error('Widget chat error:', error);
                this.addMessage('assistant', 'Sorry, I encountered an error. Please try again.', 'error');
            } finally {
                this.hideTypingIndicator();
                this.setInputState(true);
                this.messageInput.focus();
            }
        }
        
        async sendMessage(message) {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.csrfToken,
                },
                body: JSON.stringify({
                    message: message,
                    session_id: this.sessionId
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        }
        
        addMessage(type, content, variant = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            if (variant) {
                messageDiv.classList.add(variant);
            }
            
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit',
                hour12: false 
            });
            
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${this.escapeHtml(content)}
                </div>
                <div class="message-time">
                    ${timeString}
                </div>
            `;
            
            this.chatMessages.appendChild(messageDiv);
            this.scrollToBottom();
        }
        
        setInputState(enabled) {
            this.messageInput.disabled = !enabled;
            this.sendButton.disabled = !enabled;
            
            if (enabled) {
                this.sendButton.textContent = 'Send';
            } else {
                this.sendButton.textContent = 'Sending...';
            }
        }
        
        showTypingIndicator() {
            this.typingIndicator.style.display = 'block';
            this.scrollToBottom();
        }
        
        hideTypingIndicator() {
            this.typingIndicator.style.display = 'none';
        }
        
        scrollToBottom() {
            setTimeout(() => {
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }, 100);
        }
        
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    }
    
    widgetChatApp = new WidgetChatApp();
}

// Auto-initialize if user is authenticated
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is authenticated (you might need to adjust this check)
    if (document.querySelector('body').classList.contains('authenticated') || 
        document.querySelector('[name=csrfmiddlewaretoken]')) {
        // Widget is available for authenticated users
        console.log('Chat widget ready');
    }
});
</script>
