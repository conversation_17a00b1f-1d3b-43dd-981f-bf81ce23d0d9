/**
 * Chat application JavaScript
 */

class ChatApp {
    constructor(config) {
        this.sessionId = config.sessionId;
        this.csrfToken = config.csrfToken;
        this.apiUrl = config.apiUrl;
        this.newSessionUrl = config.newSessionUrl;

        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.chatForm = document.getElementById('chatForm');
        this.typingIndicator = document.getElementById('typingIndicator');

        this.init();
    }

    init() {
        // Bind event listeners
        this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.handleSubmit(e);
            }
        });

        // Auto-focus input
        this.messageInput.focus();

        // Scroll to bottom
        this.scrollToBottom();
    }

    async handleSubmit(e) {
        e.preventDefault();

        const message = this.messageInput.value.trim();
        if (!message) return;

        // Disable input while processing
        this.setInputState(false);

        // Add user message to chat
        this.addMessage('user', message);

        // Clear input
        this.messageInput.value = '';

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Send message to API
            const response = await this.sendMessage(message);

            if (response.error) {
                this.addMessage('assistant', `Error: ${response.error}`, 'error');
            } else {
                // Add assistant response
                this.addMessage('assistant', response.response);

                // Update session ID if new session was created
                if (response.session_id) {
                    this.sessionId = response.session_id;
                }

                // Show action results if any
                if (response.action_results && response.action_results.length > 0) {
                    response.action_results.forEach(result => {
                        if (result.success) {
                            this.addMessage('action', result.message || 'Action completed successfully');
                        } else {
                            this.addMessage('action', result.error || 'Action failed', 'error');
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Chat error:', error);
            this.addMessage('assistant', 'Sorry, I encountered an error. Please try again.', 'error');
        } finally {
            // Hide typing indicator and re-enable input
            this.hideTypingIndicator();
            this.setInputState(true);
            this.messageInput.focus();
        }
    }

    async sendMessage(message) {
        const response = await fetch(this.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.csrfToken,
            },
            body: JSON.stringify({
                message: message,
                session_id: this.sessionId
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    addMessage(type, content, variant = null) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;

        if (variant) {
            messageDiv.classList.add(variant);
        }

        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });

        // Format content to handle newlines properly
        const formattedContent = this.formatMessageContent(content);

        messageDiv.innerHTML = `
            <div class="message-content">
                ${formattedContent}
            </div>
            <div class="message-time">
                ${timeString}
            </div>
        `;

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    setInputState(enabled) {
        this.messageInput.disabled = !enabled;
        this.sendButton.disabled = !enabled;

        if (enabled) {
            this.sendButton.textContent = 'Send';
        } else {
            this.sendButton.textContent = 'Sending...';
        }
    }

    showTypingIndicator() {
        this.typingIndicator.style.display = 'block';
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        this.typingIndicator.style.display = 'none';
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatMessageContent(content) {
        // Escape HTML first
        const escaped = this.escapeHtml(content);

        // Convert newlines to <br> tags for proper display
        return escaped.replace(/\n/g, '<br>');
    }
}

// Global function for starting new session
async function startNewSession() {
    try {
        const response = await fetch(window.chatApp.newSessionUrl, {
            method: 'POST',
            headers: {
                'X-CSRFToken': window.chatApp.csrfToken,
            }
        });

        if (response.ok) {
            // Reload the page to start fresh
            window.location.reload();
        } else {
            alert('Failed to create new session. Please try again.');
        }
    } catch (error) {
        console.error('Error creating new session:', error);
        alert('Failed to create new session. Please try again.');
    }
}

// Make chatApp globally available
window.ChatApp = ChatApp;
