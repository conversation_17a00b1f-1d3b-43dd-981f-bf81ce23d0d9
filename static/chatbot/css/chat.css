/**
 * Chatbot CSS Styles
 */

/* Chat Container */
.chat-container {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 1200px;
    margin: 0 auto;
}

/* Chat Header */
.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-header h4 {
    margin: 0 0 5px 0;
    font-weight: 600;
}

.chat-header small {
    opacity: 0.8;
}

/* Messages Area */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: white;
    scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Message Styles */
.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message.action {
    justify-content: center;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    line-height: 1.4;
    font-size: 14px;
    position: relative;
}

.message.user .message-content {
    background: #007bff;
    color: white;
    border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
    background: #e9ecef;
    color: #333;
    border-bottom-left-radius: 4px;
}

.message.action .message-content {
    background: #28a745;
    color: white;
    border-radius: 8px;
    max-width: 80%;
    text-align: center;
}

.message.error .message-content {
    background: #dc3545;
    color: white;
}

.message-time {
    font-size: 0.75rem;
    color: #6c757d;
    margin: 5px 10px 0;
    align-self: flex-end;
}

/* Input Area */
.chat-input-container {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.chat-input-form {
    display: flex;
    gap: 10px;
    max-width: 100%;
}

.chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ced4da;
    border-radius: 25px;
    outline: none;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    resize: none;
    min-height: 44px;
    max-height: 120px;
}

.chat-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.chat-input:disabled {
    background-color: #e9ecef;
    opacity: 0.6;
}

.send-button {
    padding: 12px 20px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s, transform 0.1s;
    min-width: 80px;
}

.send-button:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-1px);
}

.send-button:active {
    transform: translateY(0);
}

.send-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

/* Typing Indicator */
.typing-indicator {
    display: none;
    padding: 10px 20px;
    font-style: italic;
    color: #6c757d;
    font-size: 14px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* Session Info */
.session-info {
    background: white;
    padding: 15px 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.session-info h5 {
    margin: 0 0 10px 0;
    color: #333;
    font-weight: 600;
}

.session-info p {
    margin: 0 0 10px 0;
    color: #666;
    line-height: 1.5;
}

.session-info small {
    color: #999;
    font-size: 12px;
}

.new-session-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
    font-size: 12px;
    transition: background-color 0.2s;
}

.new-session-btn:hover {
    background: #218838;
}

/* Error Messages */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
    border: 1px solid #f5c6cb;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-container {
        height: calc(100vh - 150px);
        border-radius: 0;
        margin: 0;
    }
    
    .message-content {
        max-width: 85%;
        font-size: 13px;
    }
    
    .chat-input-container {
        padding: 15px;
    }
    
    .session-info {
        margin: 10px;
        padding: 12px 15px;
    }
    
    .chat-input-form {
        gap: 8px;
    }
    
    .send-button {
        padding: 10px 16px;
        min-width: 70px;
    }
}

@media (max-width: 480px) {
    .chat-header {
        padding: 15px;
    }
    
    .chat-messages {
        padding: 15px;
    }
    
    .message-content {
        max-width: 90%;
        padding: 10px 14px;
        font-size: 13px;
    }
    
    .chat-input {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* Widget Styles */
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-widget .chat-messages {
    height: 350px;
    padding: 15px;
}

.chat-widget .chat-input-container {
    padding: 15px;
}

.chat-widget-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    transition: transform 0.2s, background-color 0.2s;
}

.chat-widget-toggle:hover {
    background: #0056b3;
    transform: scale(1.1);
}

@media (max-width: 480px) {
    .chat-widget {
        width: calc(100vw - 20px);
        height: calc(100vh - 40px);
        bottom: 10px;
        right: 10px;
        left: 10px;
    }
}
