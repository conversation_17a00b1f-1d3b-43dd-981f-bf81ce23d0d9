# HRMS Chatbot - Complete Setup Guide

## ✅ **Implementation Complete!**

Your HRMS chatbot has been successfully implemented using the official OpenAI Python library to connect with OpenAI's API. The chatbot reads the API key directly from environment variables for maximum security and flexibility.

## 🚀 **Quick Start**

### 1. **Set Your OpenAI API Key**

Add your OpenAI API key to your environment:

**Option A: Using .env file (Recommended)**
```bash
# Add to your .env file
OPENAI_API_KEY=your_openai_api_key_here
```

**Option B: Using system environment variables**
```bash
# Linux/Mac
export OPENAI_API_KEY=your_openai_api_key_here

# Windows
set OPENAI_API_KEY=your_openai_api_key_here
```

### 2. **Test the Integration**

```bash
# Test basic functionality
python manage.py test_chatbot

# Test OpenAI integration
python manage.py test_openai

# Test with custom message
python manage.py test_openai --message "Help me apply for leave"
```

### 3. **Access the Chatbot**

- **Simple Interface:** `http://localhost:8000/chatbot/simple/`
- **Full Interface:** `http://localhost:8000/chatbot/` (requires login)
- **Via Sidebar:** Click "HRMS Assistant" in the navigation

## 🔧 **Technical Implementation**

### **Key Features:**
- ✅ **Official OpenAI Library Integration** - Uses the official OpenAI Python client
- ✅ **Environment Variable Configuration** - Secure API key management
- ✅ **Graceful Fallback** - Works without API key (shows configuration message)
- ✅ **Permission-based Actions** - Only executes actions user has permission for
- ✅ **Comprehensive Error Handling** - API errors, authentication issues, timeouts
- ✅ **Function Calling Support** - Can execute HR actions like applying for leave

### **Environment Variables:**
```env
# Required for AI functionality
OPENAI_API_KEY=your_api_key_here

# Optional (defaults provided)
OPENAI_MODEL=gpt-3.5-turbo
CHATBOT_MAX_TOKENS=1000
CHATBOT_TEMPERATURE=0.7
```

## 💬 **How to Use**

### **Example Conversations:**

1. **Leave Management:**
   - "What's my leave balance?"
   - "I want to apply for sick leave from Dec 25 to Dec 27"
   - "Show me my leave history"

2. **Project Information:**
   - "Show me my current projects"
   - "What's the status of project ID 5?"

3. **Employee Information:**
   - "Show me my profile"
   - "Who is my reporting manager?"

4. **Attendance:**
   - "Check my attendance for this month"
   - "I need to request attendance modification"

## 🛠 **Files Modified/Created**

### **Core Implementation:**
- `chatbot/services.py` - **Updated** to use requests instead of OpenAI client
- `chatbot/management/commands/test_openai.py` - **New** test command
- `horilla/settings.py` - **Updated** with environment variable configuration
- `chatbot/README.md` - **Updated** documentation

### **Key Changes Made:**
1. **Uses official OpenAI Python library** for reliable API integration
2. **Added environment variable reading** with `os.getenv()` as primary source
3. **Improved error handling** for API and authentication errors
4. **Added comprehensive testing** with new management command
5. **Updated documentation** with new setup instructions

## 🔍 **Troubleshooting**

### **Common Issues:**

1. **"AI service is not configured" message:**
   - Set the `OPENAI_API_KEY` environment variable
   - Restart the Django server after setting the variable

2. **"Network difficulties" message:**
   - Check internet connection
   - Verify API key is valid
   - Check OpenAI service status

3. **Permission errors:**
   - Ensure user is logged in
   - Check user has appropriate permissions in Django admin

### **Testing Commands:**
```bash
# Check if API key is detected
python manage.py test_openai

# Test specific functionality
python manage.py test_chatbot

# Check server logs for errors
python manage.py runserver
```

## 🎯 **Next Steps**

1. **Add your OpenAI API key** to enable full AI functionality
2. **Test the interface** at `http://localhost:8000/chatbot/simple/`
3. **Customize AI behavior** by modifying system context in `services.py`
4. **Add more actions** by extending the permission checker
5. **Monitor usage** through Django admin interface

## 📊 **Monitoring & Analytics**

- **Chat Sessions:** View in Django admin under "Chatbot > Chat sessions"
- **Messages:** Track all conversations and responses
- **Actions:** Monitor what actions users are requesting
- **Errors:** Check Django logs for API errors or issues

## 🔐 **Security Notes**

- ✅ API key is read from environment variables (not stored in code)
- ✅ All actions are permission-checked before execution
- ✅ User input is validated before sending to OpenAI
- ✅ Error messages don't expose sensitive information

---

**🎉 Your HRMS Chatbot is now ready to use!**

The chatbot will work immediately with basic functionality. Add your OpenAI API key to enable full AI-powered conversations and automated actions.
